/*
       Licensed to the Apache Software Foundation (ASF) under one
       or more contributor license agreements.  See the NOTICE file
       distributed with this work for additional information
       regarding copyright ownership.  The ASF licenses this file
       to you under the Apache License, Version 2.0 (the
       "License"); you may not use this file except in compliance
       with the License.  You may obtain a copy of the License at

         http://www.apache.org/licenses/LICENSE-2.0

       Unless required by applicable law or agreed to in writing,
       software distributed under the License is distributed on an
       "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
       KIND, either express or implied.  See the License for the
       specific language governing permissions and limitations
       under the License.
*/

// This is an empty project used to provide Gradle Tooling
// Using the main project which loads AGP will enforce a minimum version
// requirement on the end-user, requiring a gradle install that satisfies AGP
// version requirements.
// To avoid that, we utilise this empty project of which we can
// freely run the gradle wrapper task against to obtain the 
// wrapper at the desired version, without being restricted by AGP's version
// requirements.
// Of course, the installed wrapper must still be of at least the minimum
// required version of AGP for the build to work correctly.
