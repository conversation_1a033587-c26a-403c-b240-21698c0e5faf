/* إعادة تعيين القيم الافتراضية */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* إعداد الخط والاتجاه */
body {
  font-family: 'Poppins', sans-serif;
  direction: rtl;
  background-color: #121212;
  color: #eee;
  line-height: 1.6;
}

/* الوضع الليلي يُطبق مباشرة على body */
.dark-mode {
  background-color: #121212;
  color: #eee;
}

/* رأس الصفحة */
header {
  background: linear-gradient(90deg, #072342, #00B4DB);
  padding: 20px;
  text-align: center;
  color: #fff;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}
h1 {
  font-size: 1.8em;
}

/* زر الإعدادات ولوحة الإعدادات */
#settingsBtn {
  display: none;
  position: fixed;
  top: 10px;
  right: 10px;
  padding: 10px 15px;
  background-color: #333;
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  z-index: 500;
  transition: background-color 0.3s;
}
#settingsBtn:hover {
  background-color: #555;
}

.settings-panel {
  position: fixed;
  top: 60px;
  right: 10px;
  background-color: #1f1f1f;
  padding: 15px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  z-index: 500;
  color: #eee;
}
.setting-item {
  margin-bottom: 10px;
}
.hidden {
  display: none;
}

/* قائمة الأيام */
#days-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12px;
  margin: 20px 10px;
  max-width: 100%;
}
.day-button {
  background-color: #2c2c2c;
  border: 1px solid #444;
  padding: 8px 6px;
  width: 85px;
  min-height: 65px;
  cursor: pointer;
  border-radius: 6px;
  text-align: center;
  transition: transform 0.15s, box-shadow 0.15s, background-color 0.2s;
  color: #eee;
  font-size: 0.85em;
  line-height: 1.2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.day-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 8px rgba(0,0,0,0.3);
  background-color: #3a3a3a;
}
.day-button.selected {
  background-color: #00aaff;
  border-color: #00aaff;
  box-shadow: 0 2px 6px rgba(0, 170, 255, 0.3);
}

/* حاوية الأيام المقسمة */
.split-day-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: center;
  justify-content: center;
  background-color: #1a1a1a;
  border-radius: 8px;
  padding: 8px;
  border: 1px solid #333;
  width: 140px;
}

/* أزرار الفترات الزمنية */
.split-button {
  width: 120px;
  padding: 8px;
  font-size: 0.85em;
  min-height: 45px;
  position: relative;
  border-radius: 6px;
  line-height: 1.2;
  text-align: center;
}

.time-period {
  font-size: 0.8em;
  color: #ddd;
  font-weight: 500;
  margin-top: 3px;
  display: block;
}

.morning-button {
  border-top: 3px solid #ffa500;
  background: linear-gradient(135deg, #2c2c2c 0%, #3a3a3a 100%);
}

.evening-button {
  border-bottom: 3px solid #4169e1;
  background: linear-gradient(135deg, #2c2c2c 0%, #3a3a3a 100%);
}

.morning-button:hover {
  background: linear-gradient(135deg, #3a3a3a 0%, #4a4a4a 100%);
  border-top-color: #ffb84d;
}

.evening-button:hover {
  background: linear-gradient(135deg, #3a3a3a 0%, #4a4a4a 100%);
  border-bottom-color: #5a7ae6;
}

.morning-button.selected {
  background: linear-gradient(135deg, #ff8c00 0%, #ffa500 100%);
  border-top-color: #ff8c00;
  color: #fff;
  box-shadow: 0 2px 6px rgba(255, 140, 0, 0.3);
}

.evening-button.selected {
  background: linear-gradient(135deg, #4169e1 0%, #5a7ae6 100%);
  border-bottom-color: #4169e1;
  color: #fff;
  box-shadow: 0 2px 6px rgba(65, 105, 225, 0.3);
}

/* تحسين عرض النص في الأزرار المقسمة */
.split-button strong {
  font-size: 0.9em;
  display: block;
  margin-bottom: 2px;
  font-weight: 600;
}

.split-button br {
  line-height: 1.2;
}

/* تحسين التباعد والوضوح */
.split-day-container .day-name {
  font-size: 0.75em;
  color: #aaa;
  margin-bottom: 4px;
  font-weight: 500;
  text-align: center;
}

/* تحسين التباعد والمحاذاة */
.day-button strong {
  font-weight: 600;
  margin-bottom: 2px;
}

/* تحسين الانتقالات */
.day-button, .split-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسين الظلال */
.day-button:active, .split-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

/* زر عرض جميع الحجوزات */
.all-appointments-btn {
  background-color: #2c2c2c;
  border: 1px solid #444;
  padding: 12px 20px;
  cursor: pointer;
  border-radius: 8px;
  text-align: center;
  transition: transform 0.2s, box-shadow 0.2s;
  color: #eee;
  margin: 10px auto;
  display: block;
}
.all-appointments-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

/* شريط البحث */
.search-container {
  text-align: center;
  margin: 5px 0;
}
#search-input {
  width: 80%;
  max-width: 400px;
  padding: 8px 10px;
  border: 1px solid #444;
  border-radius: 4px;
  background-color: #1f1f1f;
  color: #eee;
}

/* عرض الحجوزات */
.appointments-display {
  display: flex ;
  flex-direction: column;
  gap: 3px;
  padding: 5px 15px;
  background-color: #1f1f1f;
  border: 1px solid #444;
  margin: 5px auto 20px auto;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  min-height: 100%  ;
}
.appointments-display p {
  margin: 0;
  color: #aaa;
}

/* بطاقة الحجز */
.appointment {
  display: flex;
  align-items: center;
  background-color: #2c2c2c;
  padding: 2px;
  border: 1px solid #444;
  border-radius: 6px;
  gap: 12px;
  width: 100%;
  transition: box-shadow 0.2s;
}
.appointment:hover {
  background-color: #333;
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}
.sequence {
  font-weight: 600;
  margin-right: 5px;
  width: 5px;
}

/* حاوية الإجراء */
.procedure-container {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
}
.syringe {
  cursor: pointer;
  font-size: 1.2em;
  margin-right: 2px;
}
.syringe:hover{
  border-radius: 10px;
  color: #072342;
  background-color: #444;
}
.procedure-number-display {
  font-size: 0.9em;
  font-weight: 600;
  color: #00aaff;
  margin: 0;
  border: 0;
  width: 5px;
}
.procedure-number-display:hover {
  background-color: #333;
}

/* نافذة اختيار رقم الإجراء */
.procedure-popup {
  position: absolute;
  top: 0;
  left: 100%;
  background-color: #2c2c2c;
  border: 1px solid #444;
  padding: 5px;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  z-index: 100;
  box-shadow: 0 2px 6px rgba(0,0,0,0.3);
}
.procedure-popup:hover {
column-fill: #333;

}

/* حاوية معلومات المريض */
.info-container {
  flex: 1;
  display: flex;
  padding: 1px 4px;
  border-radius: 4px;
  cursor: pointer;
  gap: 1px;
  font-size: 0.9em;
  background-color: transparent;
}

.patient-name {
  font-size: 1.1em;
  font-weight: 500;
  width: 250px;
}
.details {
  font-size: 1.1em;
  color: #ccc;
}

/* تنسيق مربعات النص */
input[type="text"],
textarea,
select {
  width: 100%;
  padding: 8px 10px;
  margin-bottom: 10px;
  border: 1px solid #444;
  border-radius: 4px;
  background-color: #1f1f1f;
  color: #eee;
}
textarea {
  resize: vertical;
}

/* تنسيق المودالات */
.modal {
  display: none;
  position: fixed;
  z-index: 200;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.5);
  animation: fadeIn 0.3s;
}
@keyframes fadeIn {
  from {opacity: 0;}
  to {opacity: 1;}
}
.modal-content {
  background-color: #2c2c2c;
  margin: 10% auto;
  padding: 20px;
  border-radius: 8px;
  width: 320px;
  position: relative;
  box-shadow: 0 4px 12px rgba(0,0,0,0.4);
  animation: slideDown 0.3s;
  color: #eee;
}
@keyframes slideDown {
  from {transform: translateY(-20px); opacity: 0;}
  to {transform: translateY(0); opacity: 1;}
}
.close-button {
  color: #aaa;
  float: left;
  font-size: 28px;
  font-weight: 600;
  cursor: pointer;
}
.close-button:hover {
  color: #fff;
}
.submit-btn {
  background-color: #00aaff;
  border: none;
  color: #fff;
  padding: 10px;
  width: 100%;
  margin-top: 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
  transition: background 0.3s;
}
.submit-btn:hover {
  background-color: #0088cc;
}

/* أزرار التحكم */
.button-container {
  text-align: center;
  margin-bottom: 20px;
}
#add-appointment-btn,
#export-word-btn {
  background-color: #28a745;
  border: none;
  color: #fff;
  padding: 10px 20px;
  font-size: 1em;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.3s;
  margin: 5px;
}
#add-appointment-btn:hover,
#export-word-btn:hover {
  background-color: #1e7e34;
}

/* تنسيق أزرار التعديل والحذف داخل بطاقة الحجز */
.btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1em;
  color: #eee;
  margin: 0px 10px;

}
.btn.edit:hover {
  color: #00aaff;
}
.btn.delete:hover {
  color: red;
}

/* تنسيق بطاقة الحجز في مودال "جميع الحجوزات" */
.appointment-card {
  border: 1px solid #444;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 10px;
  color: #eee;
}

/* استجابة للشاشات المختلفة */
@media (max-width: 768px) {
  #days-container {
    gap: 10px;
    margin: 15px 5px;
  }

  .day-button {
    width: 75px;
    min-height: 60px;
    padding: 6px 4px;
    font-size: 0.8em;
  }

  .split-day-container {
    width: 110px;
    padding: 6px;
    gap: 4px;
  }

  .split-button {
    width: 100px;
    min-height: 40px;
    padding: 6px;
    font-size: 0.75em;
  }

  .time-period {
    font-size: 0.7em;
  }
}

@media (max-width: 480px) {
  #days-container {
    gap: 8px;
    margin: 10px 3px;
  }

  .day-button {
    width: 65px;
    min-height: 55px;
    padding: 5px 3px;
    font-size: 0.75em;
  }

  .split-day-container {
    width: 90px;
    padding: 4px;
    gap: 3px;
  }

  .split-button {
    width: 80px;
    min-height: 35px;
    padding: 4px;
    font-size: 0.65em;
  }

  .time-period {
    font-size: 0.6em;
  }
}

@media (min-width: 1200px) {
  #days-container {
    gap: 15px;
    margin: 25px 15px;
  }

  .day-button {
    width: 95px;
    min-height: 70px;
    padding: 10px 8px;
    font-size: 0.9em;
  }

  .split-day-container {
    width: 160px;
    padding: 10px;
    gap: 8px;
  }

  .split-button {
    width: 140px;
    min-height: 50px;
    padding: 10px;
    font-size: 0.9em;
  }

  .time-period {
    font-size: 0.85em;
  }
}
