/* Licensed to the Apache Software Foundation (ASF) under one
   or more contributor license agreements.  See the NOTICE file
   distributed with this work for additional information
   regarding copyright ownership.  The ASF licenses this file
   to you under the Apache License, Version 2.0 (the
   "License"); you may not use this file except in compliance
   with the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing,
   software distributed under the License is distributed on an
   "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
   KIND, either express or implied.  See the License for the
   specific language governing permissions and limitations
   under the License.
*/

buildscript {
    apply from: 'cordova.gradle'
    apply from: 'repositories.gradle'

    repositories repos

    dependencies {
        // Android Gradle Plugin (AGP) Build Tools
        classpath "com.android.tools.build:gradle:${cordovaConfig.AGP_VERSION}"
    }

    cdvHelpers.verifyCordovaConfigForBuild()
}

allprojects {
    def hasRepositoriesGradle = file('repositories.gradle').exists()
    if (hasRepositoriesGradle) {
        apply from: 'repositories.gradle'
    } else {
        apply from: "${project.rootDir}/repositories.gradle"
    }

    repositories repos
}

apply plugin: 'com.android.library'

android {
    namespace 'org.apache.cordova'

    compileSdkVersion cordovaConfig.COMPILE_SDK_VERSION
    buildToolsVersion cordovaConfig.BUILD_TOOLS_VERSION

    compileOptions {
        sourceCompatibility JavaLanguageVersion.of(cordovaConfig.JAVA_SOURCE_COMPATIBILITY)
        targetCompatibility JavaLanguageVersion.of(cordovaConfig.JAVA_TARGET_COMPATIBILITY)
    }

    // For the Android Cordova Lib, we allow changing the minSdkVersion, but it is at the users own risk
    defaultConfig {
        minSdkVersion cordovaConfig.MIN_SDK_VERSION
    }

    sourceSets {
        main {
            manifest.srcFile 'AndroidManifest.xml'
            java.srcDirs = ['src']
            resources.srcDirs = ['src']
            aidl.srcDirs = ['src']
            renderscript.srcDirs = ['src']
            res.srcDirs = ['res']
            assets.srcDirs = ['assets']
        }
    }

    packagingOptions {
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/NOTICE'
    }

    publishing {
        singleVariant('release') {
            withSourcesJar()
        }
    }
}

dependencies {
    api "androidx.appcompat:appcompat:${cordovaConfig.ANDROIDX_APP_COMPAT_VERSION}"
    implementation "androidx.webkit:webkit:${cordovaConfig.ANDROIDX_WEBKIT_VERSION}"
    implementation "androidx.core:core-splashscreen:${cordovaConfig.ANDROIDX_CORE_SPLASHSCREEN_VERSION}"
}

/**
 * In a project created though CLI, the `cordova-publish.gradle` file is not copied to the `framework` dir.
 * App development (CLI) projects can not and should not publish our framework.
 * In this case, there is no need for the gradle build process to know about the publish process.
 */
def cordovaPublishGradle = './cordova-publish.gradle'
if(file(cordovaPublishGradle).exists()) {
    apply from: cordovaPublishGradle
}
